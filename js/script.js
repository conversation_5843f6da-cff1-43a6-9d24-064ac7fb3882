// DOM elements
const yesBtn = document.getElementById('yesBtn');
const noBtn = document.getElementById('noBtn');
const hoverMessage = document.getElementById('hoverMessage');
const celebrationOverlay = document.getElementById('celebrationOverlay');
const confettiCanvas = document.getElementById('confettiCanvas');
const ctx = confettiCanvas.getContext('2d');

// Set canvas size
function resizeCanvas() {
    confettiCanvas.width = window.innerWidth;
    confettiCanvas.height = window.innerHeight;
}

resizeCanvas();
window.addEventListener('resize', resizeCanvas);

// Confetti particles array
let confettiParticles = [];

// Confetti particle class
class ConfettiParticle {
    constructor() {
        this.x = Math.random() * confettiCanvas.width;
        this.y = -10;
        this.vx = (Math.random() - 0.5) * 6;
        this.vy = Math.random() * 3 + 2;
        this.gravity = 0.1;
        this.friction = 0.99;
        this.size = Math.random() * 8 + 4;
        this.color = this.getRandomColor();
        this.rotation = Math.random() * 360;
        this.rotationSpeed = (Math.random() - 0.5) * 10;
    }

    getRandomColor() {
        const colors = [
            '#ff6b9d', '#ff8fab', '#ffc1cc', '#ffb3ba', 
            '#ff69b4', '#ff1493', '#ffd1dc', '#ffb6c1',
            '#ff91a4', '#ff7f9a'
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    update() {
        this.x += this.vx;
        this.y += this.vy;
        this.vy += this.gravity;
        this.vx *= this.friction;
        this.rotation += this.rotationSpeed;
    }

    draw() {
        ctx.save();
        ctx.translate(this.x, this.y);
        ctx.rotate(this.rotation * Math.PI / 180);
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.size / 2, -this.size / 2, this.size, this.size);
        ctx.restore();
    }
}

// Create confetti
function createConfetti() {
    for (let i = 0; i < 150; i++) {
        confettiParticles.push(new ConfettiParticle());
    }
}

// Animate confetti
function animateConfetti() {
    ctx.clearRect(0, 0, confettiCanvas.width, confettiCanvas.height);
    
    for (let i = confettiParticles.length - 1; i >= 0; i--) {
        const particle = confettiParticles[i];
        particle.update();
        particle.draw();
        
        // Remove particles that are off screen
        if (particle.y > confettiCanvas.height + 10) {
            confettiParticles.splice(i, 1);
        }
    }
    
    if (confettiParticles.length > 0) {
        requestAnimationFrame(animateConfetti);
    }
}

// No button hover effects
let noButtonMoveCount = 0;
const maxMoves = 3;

noBtn.addEventListener('mouseenter', function() {
    // Show hover message
    hoverMessage.classList.add('show');
    
    // Move the button away
    if (noButtonMoveCount < maxMoves) {
        moveNoButton();
        noButtonMoveCount++;
    } else {
        // After 3 moves, make it really hard to click
        makeNoButtonElusive();
    }
});

noBtn.addEventListener('mouseleave', function() {
    // Hide hover message after a delay
    setTimeout(() => {
        hoverMessage.classList.remove('show');
    }, 1000);
});

function moveNoButton() {
    const container = document.querySelector('.buttons-container');
    const containerRect = container.getBoundingClientRect();
    const buttonRect = noBtn.getBoundingClientRect();
    
    // Calculate safe movement area
    const maxX = containerRect.width - buttonRect.width;
    const maxY = containerRect.height - buttonRect.height;
    
    // Generate random position
    const newX = Math.random() * Math.min(maxX, 200) - 100;
    const newY = Math.random() * Math.min(maxY, 100) - 50;
    
    // Apply transform
    noBtn.style.transform = `translate(${newX}px, ${newY}px)`;
    
    // Add shake effect
    noBtn.style.animation = 'runAway 0.5s ease-in-out';
    
    setTimeout(() => {
        noBtn.style.animation = '';
    }, 500);
}

function makeNoButtonElusive() {
    // Make the button smaller and more transparent
    noBtn.style.transform = 'scale(0.8)';
    noBtn.style.opacity = '0.7';
    
    // Add continuous movement
    let moveInterval = setInterval(() => {
        const randomX = (Math.random() - 0.5) * 300;
        const randomY = (Math.random() - 0.5) * 200;
        noBtn.style.transform = `translate(${randomX}px, ${randomY}px) scale(0.8)`;
    }, 1000);
    
    // Store interval to clear it later if needed
    noBtn.moveInterval = moveInterval;
}

// Yes button click event
yesBtn.addEventListener('click', function() {
    // Clear any no button intervals
    if (noBtn.moveInterval) {
        clearInterval(noBtn.moveInterval);
    }
    
    // Show celebration
    celebrationOverlay.classList.add('show');
    
    // Create and animate confetti
    createConfetti();
    animateConfetti();
    
    // Add celebration sound effect (optional - you can add audio file)
    // playSound('celebration.mp3');
    
    // Add extra celebration effects
    setTimeout(() => {
        createMoreConfetti();
    }, 1000);
    
    setTimeout(() => {
        createMoreConfetti();
    }, 2000);
});

function createMoreConfetti() {
    for (let i = 0; i < 50; i++) {
        confettiParticles.push(new ConfettiParticle());
    }
    if (confettiParticles.length > 0) {
        animateConfetti();
    }
}

// No button click event (just in case someone manages to click it)
noBtn.addEventListener('click', function(e) {
    e.preventDefault();
    
    // Show a playful message
    const playfulMessages = [
        "พยายามดีนะ! แต่ฉันบอกแล้วว่าไม่มีทางเลือก 'ไม่เอา'! 😉",
        "ไม่ได้! ลองกดปุ่มอีกอันสิ! 💖",
        "เอาน่า รู้ใจกันอยู่แล้วว่าคำตอบที่ถูกคืออะไร! 😘",
        "ปุ่มสีชมพูเรียกชื่อเธออยู่นะ! 💕"
    ];
    
    const randomMessage = playfulMessages[Math.floor(Math.random() * playfulMessages.length)];
    
    // Create temporary message
    const tempMessage = document.createElement('div');
    tempMessage.textContent = randomMessage;
    tempMessage.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(255, 182, 193, 0.95);
        color: #d63384;
        padding: 20px;
        border-radius: 15px;
        font-weight: 600;
        z-index: 1001;
        animation: fadeInOut 3s ease-in-out;
        text-align: center;
        box-shadow: 0 10px 30px rgba(214, 51, 132, 0.3);
    `;
    
    document.body.appendChild(tempMessage);
    
    // Remove message after animation
    setTimeout(() => {
        document.body.removeChild(tempMessage);
    }, 3000);
    
    // Make the no button even more elusive
    makeNoButtonElusive();
});

// Add CSS for fadeInOut animation
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInOut {
        0% { opacity: 0; transform: translate(-50%, -50%) scale(0.5); }
        20% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
        80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        100% { opacity: 0; transform: translate(-50%, -50%) scale(0.5); }
    }
`;
document.head.appendChild(style);

// Floating images function
function createFloatingImages() {
    for (let i = 0; i < 20; i++) {
        setTimeout(() => {
            const img = document.createElement('img');
            img.src = '../images/510434506_1256081902761270_5304385400308496768_n.png';
            img.alt = 'Floating image';
            img.className = 'floating-image';
            img.style.cssText = `
                position: fixed;
                width: ${Math.random() * 40 + 40}px;
                height: auto;
                opacity: 0;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                z-index: 3;
                pointer-events: none;
                animation: float ${3 + Math.random() * 4}s ease-in-out infinite;
                animation-delay: ${Math.random() * 2}s;
            `;

            document.body.appendChild(img);

            setTimeout(() => {
                img.style.opacity = '0.6';
            }, 100);

            setTimeout(() => {
                if (img.parentNode) {
                    img.remove();
                }
            }, 7000);
        }, i * 400);
    }
}

// Add some extra romantic touches
document.addEventListener('DOMContentLoaded', function() {
    // Add floating hearts periodically
    setInterval(createFloatingHeart, 3000);

    // Add floating images periodically
    createFloatingImages();
    setInterval(createFloatingImages, 15000); // Create new batch every 15 seconds
});

function createFloatingHeart() {
    const heart = document.createElement('div');
    heart.textContent = '💖';
    heart.style.cssText = `
        position: fixed;
        font-size: 2rem;
        pointer-events: none;
        z-index: 1;
        left: ${Math.random() * window.innerWidth}px;
        top: ${window.innerHeight + 50}px;
        animation: floatUp 4s linear;
        opacity: 0.6;
    `;
    
    document.body.appendChild(heart);
    
    // Remove heart after animation
    setTimeout(() => {
        if (document.body.contains(heart)) {
            document.body.removeChild(heart);
        }
    }, 4000);
}

// Add CSS for floating hearts
const heartStyle = document.createElement('style');
heartStyle.textContent = `
    @keyframes floatUp {
        0% { 
            transform: translateY(0) rotate(0deg);
            opacity: 0.6;
        }
        50% {
            opacity: 0.8;
        }
        100% { 
            transform: translateY(-${window.innerHeight + 100}px) rotate(360deg);
            opacity: 0;
        }
    }
`;
document.head.appendChild(heartStyle);

// Make the page shareable
function copyToClipboard() {
    navigator.clipboard.writeText(window.location.href).then(() => {
        console.log('Link copied to clipboard!');
    });
}

// Add keyboard shortcuts for fun
document.addEventListener('keydown', function(e) {
    if (e.key === 'y' || e.key === 'Y') {
        yesBtn.click();
    }
});
