// DOM elements
const yesBtn = document.getElementById('yesBtn');
const noBtn = document.getElementById('noBtn');
const hoverMessage = document.getElementById('hoverMessage');
const celebrationOverlay = document.getElementById('celebrationOverlay');
const confettiCanvas = document.getElementById('confettiCanvas');
const ctx = confettiCanvas.getContext('2d');

// Set canvas size
function resizeCanvas() {
    confettiCanvas.width = window.innerWidth;
    confettiCanvas.height = window.innerHeight;
}

resizeCanvas();
window.addEventListener('resize', resizeCanvas);

// Confetti particles array
let confettiParticles = [];

// Confetti particle class
class ConfettiParticle {
    constructor() {
        this.x = Math.random() * confettiCanvas.width;
        this.y = -10;
        this.vx = (Math.random() - 0.5) * 6;
        this.vy = Math.random() * 3 + 2;
        this.gravity = 0.1;
        this.friction = 0.99;
        this.size = Math.random() * 8 + 4;
        this.color = this.getRandomColor();
        this.rotation = Math.random() * 360;
        this.rotationSpeed = (Math.random() - 0.5) * 10;
    }

    getRandomColor() {
        const colors = [
            '#ff6b9d', '#ff8fab', '#ffc1cc', '#ffb3ba', 
            '#ff69b4', '#ff1493', '#ffd1dc', '#ffb6c1',
            '#ff91a4', '#ff7f9a'
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    update() {
        this.x += this.vx;
        this.y += this.vy;
        this.vy += this.gravity;
        this.vx *= this.friction;
        this.rotation += this.rotationSpeed;
    }

    draw() {
        ctx.save();
        ctx.translate(this.x, this.y);
        ctx.rotate(this.rotation * Math.PI / 180);
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.size / 2, -this.size / 2, this.size, this.size);
        ctx.restore();
    }
}

// Create confetti
function createConfetti() {
    for (let i = 0; i < 150; i++) {
        confettiParticles.push(new ConfettiParticle());
    }
}

// Animate confetti
function animateConfetti() {
    ctx.clearRect(0, 0, confettiCanvas.width, confettiCanvas.height);
    
    for (let i = confettiParticles.length - 1; i >= 0; i--) {
        const particle = confettiParticles[i];
        particle.update();
        particle.draw();
        
        // Remove particles that are off screen
        if (particle.y > confettiCanvas.height + 10) {
            confettiParticles.splice(i, 1);
        }
    }
    
    if (confettiParticles.length > 0) {
        requestAnimationFrame(animateConfetti);
    }
}

// No button hover effects
let noButtonMoveCount = 0;
const maxMoves = 3;

noBtn.addEventListener('mouseenter', function() {
    // Show hover message
    hoverMessage.classList.add('show');
    
    // Move the button away
    if (noButtonMoveCount < maxMoves) {
        moveNoButton();
        noButtonMoveCount++;
    } else {
        // After 3 moves, make it really hard to click
        makeNoButtonElusive();
    }
});

noBtn.addEventListener('mouseleave', function() {
    // Hide hover message after a delay
    setTimeout(() => {
        hoverMessage.classList.remove('show');
    }, 1000);
});

function moveNoButton() {
    const container = document.querySelector('.buttons-container');
    const containerRect = container.getBoundingClientRect();
    const buttonRect = noBtn.getBoundingClientRect();
    
    // Calculate safe movement area
    const maxX = containerRect.width - buttonRect.width;
    const maxY = containerRect.height - buttonRect.height;
    
    // Generate random position
    const newX = Math.random() * Math.min(maxX, 200) - 100;
    const newY = Math.random() * Math.min(maxY, 100) - 50;
    
    // Apply transform
    noBtn.style.transform = `translate(${newX}px, ${newY}px)`;
    
    // Add shake effect
    noBtn.style.animation = 'runAway 0.5s ease-in-out';
    
    setTimeout(() => {
        noBtn.style.animation = '';
    }, 500);
}

function makeNoButtonElusive() {
    // Make the button smaller and more transparent
    noBtn.style.transform = 'scale(0.8)';
    noBtn.style.opacity = '0.7';
    
    // Add continuous movement
    let moveInterval = setInterval(() => {
        const randomX = (Math.random() - 0.5) * 300;
        const randomY = (Math.random() - 0.5) * 200;
        noBtn.style.transform = `translate(${randomX}px, ${randomY}px) scale(0.8)`;
    }, 1000);
    
    // Store interval to clear it later if needed
    noBtn.moveInterval = moveInterval;
}

// Add sound effect to yes button hover
yesBtn.addEventListener('mouseenter', () => playSound('hover'));

// Yes button click event
yesBtn.addEventListener('click', function() {
    // Clear any no button intervals
    if (noBtn.moveInterval) {
        clearInterval(noBtn.moveInterval);
    }

    // Show celebration
    celebrationOverlay.classList.add('show');

    // Create and animate confetti
    createConfetti();
    animateConfetti();

    // Add celebration sound effect (optional - you can add audio file)
    // playSound('celebration.mp3');

    // Add extra celebration effects
    setTimeout(() => {
        createMoreConfetti();
    }, 1000);

    setTimeout(() => {
        createMoreConfetti();
    }, 2000);

    // Initialize celebration features after a short delay to ensure DOM is ready
    setTimeout(() => {
        initializeCelebrationButtons();
        startSlideshow(); // Auto-start the slideshow
    }, 500);
});

function createMoreConfetti() {
    for (let i = 0; i < 50; i++) {
        confettiParticles.push(new ConfettiParticle());
    }
    if (confettiParticles.length > 0) {
        animateConfetti();
    }
}

// No button click event (just in case someone manages to click it)
noBtn.addEventListener('click', function(e) {
    e.preventDefault();
    
    // Show a playful message
    const playfulMessages = [
        "พยายามดีนะแหนม แต่ฉันบอกแล้วว่าไม่มีทางเลือก 'ไม่เอา'! 😉",
        "ไม่ได้ใส้กรอก! ลองกดปุ่มอีกอันสิ! 💖",
        "เอาน่าแหนม รู้ใจกันอยู่แล้วว่าคำตอบที่ถูกคืออะไร! 😘",
        "ปุ่มสีชมพูเรียกชื่อใส้กรอกอยู่นะ! 💕",
        "แหนมรู้ใจฉันอยู่แล้วใช่มั้ย? 🥰"
    ];
    
    const randomMessage = playfulMessages[Math.floor(Math.random() * playfulMessages.length)];
    
    // Create temporary message
    const tempMessage = document.createElement('div');
    tempMessage.textContent = randomMessage;
    tempMessage.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(255, 182, 193, 0.95);
        color: #d63384;
        padding: 20px;
        border-radius: 15px;
        font-weight: 600;
        z-index: 1001;
        animation: fadeInOut 3s ease-in-out;
        text-align: center;
        box-shadow: 0 10px 30px rgba(214, 51, 132, 0.3);
    `;
    
    document.body.appendChild(tempMessage);
    
    // Remove message after animation
    setTimeout(() => {
        document.body.removeChild(tempMessage);
    }, 3000);
    
    // Make the no button even more elusive
    makeNoButtonElusive();
});

// Add CSS for fadeInOut animation
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInOut {
        0% { opacity: 0; transform: translate(-50%, -50%) scale(0.5); }
        20% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
        80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        100% { opacity: 0; transform: translate(-50%, -50%) scale(0.5); }
    }
`;
document.head.appendChild(style);

// Enhanced floating images function with your photos
function createFloatingImages() {
    const imageList = [
        '../images/HEIF Image.png',
        '../images/HEIF Image 2.png',
        '../images/HEIF Image 3.png',
        '../images/HEIF Image 4.png',
        '../images/HEIF Image 5.png',
        '../images/510434506_1256081902761270_5304385400308496768_n.png'
    ];

    for (let i = 0; i < 12; i++) {
        setTimeout(() => {
            const img = document.createElement('img');
            img.src = imageList[Math.floor(Math.random() * imageList.length)];
            img.alt = 'Memory image';
            img.className = 'floating-image';
            img.style.cssText = `
                position: fixed;
                width: ${Math.random() * 50 + 60}px;
                height: ${Math.random() * 50 + 60}px;
                opacity: 0;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                z-index: 3;
                pointer-events: none;
                border-radius: 50%;
                object-fit: cover;
                animation: float ${4 + Math.random() * 3}s ease-in-out infinite;
                animation-delay: ${Math.random() * 2}s;
                box-shadow: 0 5px 15px rgba(255, 105, 180, 0.3);
                border: 2px solid rgba(255, 255, 255, 0.8);
            `;

            document.body.appendChild(img);

            setTimeout(() => {
                img.style.opacity = '0.7';
            }, 100);

            setTimeout(() => {
                if (img.parentNode) {
                    img.remove();
                }
            }, 8000);
        }, i * 500);
    }
}

// Add mouse trail hearts
document.addEventListener('mousemove', function(e) {
    if (Math.random() > 0.95) { // Only sometimes
        createTrailHeart(e.clientX, e.clientY);
    }
});

function createTrailHeart(x, y) {
    const heart = document.createElement('div');
    heart.textContent = '💕';
    heart.style.cssText = `
        position: fixed;
        left: ${x}px;
        top: ${y}px;
        font-size: 1rem;
        pointer-events: none;
        z-index: 10;
        animation: fadeUp 2s ease-out forwards;
    `;
    document.body.appendChild(heart);
    setTimeout(() => heart.remove(), 2000);
}

// Typewriter effect for the question
function typewriterEffect() {
    const question = "แหนมจะเป็นแฟนกับเค้ามั้ย?";
    const element = document.querySelector('.main-question');
    element.textContent = '';

    let i = 0;
    const timer = setInterval(() => {
        element.textContent += question[i];
        i++;
        if (i >= question.length) {
            clearInterval(timer);
            // Show buttons after question is complete
            document.querySelector('.buttons-container').style.opacity = '1';
        }
    }, 150);
}

// Rotating personal messages
const personalMessages = [
    "ทุกครั้งที่ได้คุยกับใส้กรอก หัวใจฉันเต้นแรงมาก... 💕",
    "แหนมทำให้ฉันยิ้มได้ทุกวัน ✨",
    "อยากจะดูแลใส้กรอกตลอดไป 🌸",
    "แหนมคือความสุขของฉัน 💖",
    "ใส้กรอกน่ารักที่สุดในโลก 🥰",
    "คิดถึงแหนมทุกวัน 💭",
    "อยากจะอยู่ข้างๆ ใส้กรอกตลอดไป 🌟"
];

let messageIndex = 0;
function rotatePersonalMessages() {
    const messageElement = document.querySelector('.cute-message p');
    if (messageElement) {
        messageElement.textContent = personalMessages[messageIndex];
        messageIndex = (messageIndex + 1) % personalMessages.length;
    }
}

// Add subtle sound effects
function playSound(type) {
    try {
        if (type === 'hover') {
            // Create a soft beep sound programmatically
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.value = 800;
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.1);
        }
    } catch (e) {
        // Silently fail if audio context is not supported
        console.log('Audio not supported');
    }
}

// Add special memories that appear randomly
const memories = [
    "จำได้มั้ยครั้งแรกที่เราคุยกัน แหนม? 😊",
    "ใส้กรอกหัวเราะน่ารักมาก 🥰",
    "อยากสร้างความทรงจำดีๆ ด้วยกับแหนมอีกเยอะเลย ✨",
    "แหนมมีรอยยิ้มที่สวยที่สุด 😍",
    "ใส้กรอกทำให้ทุกวันของฉันสดใส 🌞",
    "คิดถึงแหนมตอนที่เธอ... 💭"
];

function showRandomMemory() {
    if (Math.random() > 0.7) {
        const memory = memories[Math.floor(Math.random() * memories.length)];
        const memoryDiv = document.createElement('div');
        memoryDiv.textContent = memory;
        memoryDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            font-size: 0.9rem;
            color: #d63384;
            animation: slideInRight 0.5s ease-out;
            z-index: 100;
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.3);
        `;
        document.body.appendChild(memoryDiv);
        setTimeout(() => memoryDiv.remove(), 4000);
    }
}

// Add some extra romantic touches
document.addEventListener('DOMContentLoaded', function() {
    // Initialize typewriter effect
    typewriterEffect();

    // Start rotating messages after typewriter is done
    setTimeout(() => {
        setInterval(rotatePersonalMessages, 4000);
    }, 3000);

    // Add floating hearts periodically
    setInterval(createFloatingHeart, 3000);

    // Add floating images periodically
    createFloatingImages();
    setInterval(createFloatingImages, 15000); // Create new batch every 15 seconds

    // Show random memories
    setInterval(showRandomMemory, 15000);
});

function createFloatingHeart() {
    const heart = document.createElement('div');
    heart.textContent = '💖';
    heart.style.cssText = `
        position: fixed;
        font-size: 2rem;
        pointer-events: none;
        z-index: 1;
        left: ${Math.random() * window.innerWidth}px;
        top: ${window.innerHeight + 50}px;
        animation: floatUp 4s linear;
        opacity: 0.6;
    `;
    
    document.body.appendChild(heart);
    
    // Remove heart after animation
    setTimeout(() => {
        if (document.body.contains(heart)) {
            document.body.removeChild(heart);
        }
    }, 4000);
}

// Add CSS for floating hearts
const heartStyle = document.createElement('style');
heartStyle.textContent = `
    @keyframes floatUp {
        0% { 
            transform: translateY(0) rotate(0deg);
            opacity: 0.6;
        }
        50% {
            opacity: 0.8;
        }
        100% { 
            transform: translateY(-${window.innerHeight + 100}px) rotate(360deg);
            opacity: 0;
        }
    }
`;
document.head.appendChild(heartStyle);

// Make the page shareable
function copyToClipboard() {
    navigator.clipboard.writeText(window.location.href).then(() => {
        console.log('Link copied to clipboard!');
    });
}

// Enhanced celebration features
let celebrationMessageIndex = 0;
let celebrationInterval;

function startCelebrationMessages() {
    const messages = document.querySelectorAll('.celebration-message');

    // Show first message
    messages[0].classList.add('active');

    // Rotate through messages
    celebrationInterval = setInterval(() => {
        // Hide current message
        messages[celebrationMessageIndex].classList.remove('active');

        // Move to next message
        celebrationMessageIndex = (celebrationMessageIndex + 1) % messages.length;

        // Show next message
        setTimeout(() => {
            messages[celebrationMessageIndex].classList.add('active');
        }, 300);
    }, 3000);
}

function initializeCelebrationButtons() {
    console.log('Initializing celebration buttons...'); // Debug log

    // Initialize slideshow immediately
    initializeSlideshow();

    // Memory button - toggles slideshow auto-advance
    const memoryBtn = document.getElementById('memoryBtn');
    if (memoryBtn) {
        console.log('Memory button found!'); // Debug log
        memoryBtn.addEventListener('click', function() {
            console.log('Memory button clicked!'); // Debug log
            // Clear existing interval
            if (slideshowInterval) {
                clearInterval(slideshowInterval);
                slideshowInterval = null;
                memoryBtn.querySelector('.btn-text').textContent = 'เริ่มสไลด์โชว์';
                showTemporaryMessage('หยุดสไลด์โชว์แล้ว - ใช้ลูกศรหรือจุดเพื่อดูภาพ! 📸');
            } else {
                startSlideshow();
                memoryBtn.querySelector('.btn-text').textContent = 'หยุดสไลด์โชว์';
                showTemporaryMessage('เริ่มสไลด์โชว์อัตโนมัติแล้ว! ✨');
            }
        });
    } else {
        console.log('Memory button not found!'); // Debug log
    }

    // Letter button
    const letterBtn = document.getElementById('letterBtn');
    if (letterBtn) {
        console.log('Letter button found!'); // Debug log
        letterBtn.addEventListener('click', function() {
            console.log('Letter button clicked!'); // Debug log
            showLetter();
        });
    } else {
        console.log('Letter button not found!'); // Debug log
    }

    // Share button
    const shareBtn = document.getElementById('shareBtn');
    if (shareBtn) {
        console.log('Share button found!'); // Debug log
        shareBtn.addEventListener('click', function() {
            console.log('Share button clicked!'); // Debug log
            if (navigator.share) {
                navigator.share({
                    title: 'เราเป็นแฟนกันแล้ว! 💖',
                    text: 'แหนมตอบตกลงเป็นแฟนเค้าแล้ว! 🥳💕',
                    url: window.location.href
                }).then(() => {
                    showTemporaryMessage('แชร์ความสุขสำเร็จแล้ว! 🎉💕');
                }).catch(() => {
                    // Fallback to clipboard
                    copyToClipboard();
                });
            } else {
                copyToClipboard();
            }
        });
    } else {
        console.log('Share button not found!'); // Debug log
    }

    // Test button for debugging
    const testBtn = document.getElementById('testBtn');
    if (testBtn) {
        console.log('Test button found!'); // Debug log
        testBtn.addEventListener('click', function() {
            console.log('Test button clicked! Current slide:', currentSlide); // Debug log
            nextSlide();
            showTemporaryMessage('Test: Moving to next slide! Current: ' + (currentSlide + 1));
        });
    }

    function copyToClipboard() {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(window.location.href).then(() => {
                showTemporaryMessage('คัดลอกลิงก์แล้ว! แชร์ความสุขกันเลย! 📱💖');
            }).catch(() => {
                showTemporaryMessage('ไม่สามารถคัดลอกได้ กรุณาคัดลอกลิงก์เอง 😅');
            });
        } else {
            showTemporaryMessage('กรุณาคัดลอกลิงก์จาก URL bar เพื่อแชร์! 📱');
        }
    }
}

// Enhanced Slideshow functionality
let currentSlide = 0;
let slideshowInterval;
let totalSlides = 6;

function initializeSlideshow() {
    console.log('Initializing slideshow...'); // Debug log

    // Navigation dots
    const navDots = document.querySelectorAll('.nav-dot');
    console.log('Found nav dots:', navDots.length); // Debug log

    navDots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            console.log('Nav dot clicked:', index); // Debug log
            if (slideshowInterval) {
                clearInterval(slideshowInterval);
                slideshowInterval = null;
            }
            goToSlide(index);
        });
    });

    // Arrow navigation
    const prevBtn = document.getElementById('prevSlide');
    const nextBtn = document.getElementById('nextSlide');

    console.log('Prev button:', prevBtn); // Debug log
    console.log('Next button:', nextBtn); // Debug log

    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            console.log('Previous button clicked!'); // Debug log
            if (slideshowInterval) {
                clearInterval(slideshowInterval);
                slideshowInterval = null;
            }
            previousSlide();
        });
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            console.log('Next button clicked!'); // Debug log
            if (slideshowInterval) {
                clearInterval(slideshowInterval);
                slideshowInterval = null;
            }
            nextSlide();
        });
    }

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (document.getElementById('celebrationOverlay').classList.contains('show')) {
            if (e.key === 'ArrowLeft') {
                clearInterval(slideshowInterval);
                previousSlide();
            } else if (e.key === 'ArrowRight') {
                clearInterval(slideshowInterval);
                nextSlide();
            }
        }
    });

    // Touch/swipe support for mobile
    let startX = 0;
    let endX = 0;

    const slideContainer = document.querySelector('.slideshow-container');
    if (slideContainer) {
        slideContainer.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
        });

        slideContainer.addEventListener('touchend', (e) => {
            endX = e.changedTouches[0].clientX;
            handleSwipe();
        });
    }

    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = startX - endX;

        if (Math.abs(diff) > swipeThreshold) {
            clearInterval(slideshowInterval);
            if (diff > 0) {
                nextSlide(); // Swipe left = next slide
            } else {
                previousSlide(); // Swipe right = previous slide
            }
        }
    }

    // Update slide counter
    updateSlideCounter();
}

function startSlideshow() {
    // Auto-advance slideshow every 5 seconds
    slideshowInterval = setInterval(() => {
        nextSlide();
    }, 5000);
}

function nextSlide() {
    currentSlide = (currentSlide + 1) % totalSlides;
    goToSlide(currentSlide);
}

function previousSlide() {
    currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
    goToSlide(currentSlide);
}

function goToSlide(slideIndex) {
    console.log('Going to slide:', slideIndex); // Debug log

    const slides = document.querySelectorAll('.slide');
    const navDots = document.querySelectorAll('.nav-dot');

    console.log('Found slides:', slides.length); // Debug log
    console.log('Found nav dots:', navDots.length); // Debug log

    // Hide all slides
    slides.forEach((slide, index) => {
        slide.classList.remove('active');
        console.log('Removed active from slide', index); // Debug log
    });

    navDots.forEach((dot, index) => {
        dot.classList.remove('active');
        console.log('Removed active from dot', index); // Debug log
    });

    // Show selected slide
    if (slides[slideIndex]) {
        slides[slideIndex].classList.add('active');
        console.log('Added active to slide', slideIndex); // Debug log
    }
    if (navDots[slideIndex]) {
        navDots[slideIndex].classList.add('active');
        console.log('Added active to dot', slideIndex); // Debug log
    }

    currentSlide = slideIndex;
    updateSlideCounter();
}

function updateSlideCounter() {
    const currentSlideNum = document.getElementById('currentSlideNum');
    const totalSlidesNum = document.getElementById('totalSlides');

    if (currentSlideNum) {
        currentSlideNum.textContent = currentSlide + 1;
    }
    if (totalSlidesNum) {
        totalSlidesNum.textContent = totalSlides;
    }
}

function showPhotoBooth() {
    const photoBooth = document.getElementById('photoBooth');
    const coupleDate = document.getElementById('coupleDate');

    // Set current date
    const now = new Date();
    const dateStr = now.toLocaleDateString('th-TH', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    coupleDate.textContent = dateStr;

    // Show photo booth
    photoBooth.classList.remove('hidden');

    // Close button functionality
    const closeBtn = document.getElementById('closePhoto');
    closeBtn.addEventListener('click', function() {
        photoBooth.classList.add('hidden');
    });

    // Close on background click
    photoBooth.addEventListener('click', function(e) {
        if (e.target === photoBooth) {
            photoBooth.classList.add('hidden');
        }
    });
}

function createSurpriseEffect() {
    // Create multiple surprise elements
    const surprises = ['🎁', '💝', '🌹', '💍', '⭐', '✨', '🦋', '🌈'];

    for (let i = 0; i < 20; i++) {
        setTimeout(() => {
            const surprise = document.createElement('div');
            surprise.textContent = surprises[Math.floor(Math.random() * surprises.length)];
            surprise.style.cssText = `
                position: fixed;
                font-size: ${Math.random() * 30 + 20}px;
                left: ${Math.random() * window.innerWidth}px;
                top: ${Math.random() * window.innerHeight}px;
                z-index: 3000;
                pointer-events: none;
                animation: surpriseFloat 3s ease-out forwards;
            `;

            document.body.appendChild(surprise);

            setTimeout(() => {
                if (surprise.parentNode) {
                    surprise.remove();
                }
            }, 3000);
        }, i * 100);
    }

    // Show surprise message
    showTemporaryMessage('เซอร์ไพรส์! เค้ารักแหนมมากๆ นะ! 💖✨🎁');
}

function showTemporaryMessage(message) {
    const tempDiv = document.createElement('div');
    tempDiv.textContent = message;
    tempDiv.style.cssText = `
        position: fixed;
        top: 20%;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(255, 105, 180, 0.95);
        color: white;
        padding: 20px 30px;
        border-radius: 15px;
        font-weight: 600;
        z-index: 3001;
        animation: messagePopup 4s ease-in-out forwards;
        text-align: center;
        box-shadow: 0 10px 30px rgba(255, 105, 180, 0.4);
    `;

    document.body.appendChild(tempDiv);

    setTimeout(() => {
        if (tempDiv.parentNode) {
            tempDiv.remove();
        }
    }, 4000);
}

// Letter functionality
function showLetter() {
    const letterOverlay = document.getElementById('letterOverlay');
    const envelope = document.getElementById('envelope');

    if (letterOverlay) {
        // Disable body scroll
        document.body.style.overflow = 'hidden';

        // Show the letter overlay with enhanced animation
        letterOverlay.style.display = 'flex';

        // Trigger the show animation after a brief moment for smooth transition
        requestAnimationFrame(() => {
            letterOverlay.classList.add('show');
        });

        // Add click handler to envelope to open it
        const handleEnvelopeClick = function(e) {
            e.stopPropagation(); // Prevent background click
            if (!envelope.classList.contains('opened')) {
                // Step 1: Envelope opening animation
                envelope.style.animation = 'envelopeShake 0.8s ease-in-out';

                // Step 2: Open the flap after shake
                setTimeout(() => {
                    envelope.classList.add('opened');
                    envelope.style.animation = '';
                }, 400);

                // Step 3: Letter emerges after flap opens
                setTimeout(() => {
                    // The letter emergence is handled by CSS animation
                    console.log('Letter emerging from envelope...');
                }, 800);

                // Remove click handler after opening
                envelope.removeEventListener('click', handleEnvelopeClick);

                // Add click handler to the close button on letter paper
                const letterPaper = document.getElementById('letterPaper');
                const handleLetterClose = function(e) {
                    // Check if click is on the close button area (top-right corner)
                    const rect = letterPaper.getBoundingClientRect();
                    const closeButtonArea = {
                        x: rect.right - 55,
                        y: rect.top,
                        width: 55,
                        height: 55
                    };

                    if (e.clientX >= closeButtonArea.x && e.clientX <= closeButtonArea.x + closeButtonArea.width &&
                        e.clientY >= closeButtonArea.y && e.clientY <= closeButtonArea.y + closeButtonArea.height) {
                        closeLetter();
                    }
                };

                letterPaper.addEventListener('click', handleLetterClose);
                letterOverlay._letterCloseHandler = handleLetterClose;
            }
        };

        envelope.addEventListener('click', handleEnvelopeClick);
        envelope.style.cursor = 'pointer';



        // Close on background click
        const handleBackgroundClick = function(e) {
            if (e.target === letterOverlay) {
                closeLetter();
            }
        };
        letterOverlay.addEventListener('click', handleBackgroundClick);

        // Close on escape key
        const handleEscapeKey = function(e) {
            if (e.key === 'Escape') {
                closeLetter();
            }
        };
        document.addEventListener('keydown', handleEscapeKey);

        // Store event listeners for cleanup
        letterOverlay._backgroundClickHandler = handleBackgroundClick;
        letterOverlay._escapeKeyHandler = handleEscapeKey;
        letterOverlay._envelopeClickHandler = handleEnvelopeClick;
    }
}

function closeLetter() {
    const letterOverlay = document.getElementById('letterOverlay');
    const envelope = document.getElementById('envelope');
    const letterPaper = document.getElementById('letterPaper');

    if (letterOverlay && letterOverlay.classList.contains('show')) {
        // Step 1: Animate letter disappearing back into envelope
        letterPaper.style.animation = 'letterDisappear 1s cubic-bezier(0.4, 0, 0.2, 1) forwards';

        // Step 2: Close envelope flap after letter disappears
        setTimeout(() => {
            envelope.classList.remove('opened');
        }, 600);

        // Step 3: Fade out overlay
        setTimeout(() => {
            letterOverlay.classList.remove('show');
        }, 800);

        // Step 4: Clean up after all animations complete
        setTimeout(() => {
            letterOverlay.style.display = 'none';
            envelope.style.animation = '';
            letterPaper.style.animation = '';
            envelope.style.cursor = 'pointer'; // Reset cursor for next time
            document.body.style.overflow = '';

            // Clean up event listeners
            if (letterOverlay._backgroundClickHandler) {
                letterOverlay.removeEventListener('click', letterOverlay._backgroundClickHandler);
                delete letterOverlay._backgroundClickHandler;
            }
            if (letterOverlay._escapeKeyHandler) {
                document.removeEventListener('keydown', letterOverlay._escapeKeyHandler);
                delete letterOverlay._escapeKeyHandler;
            }
            if (letterOverlay._envelopeClickHandler) {
                envelope.removeEventListener('click', letterOverlay._envelopeClickHandler);
                delete letterOverlay._envelopeClickHandler;
            }
            if (letterOverlay._letterCloseHandler) {
                const letterPaper = document.getElementById('letterPaper');
                letterPaper.removeEventListener('click', letterOverlay._letterCloseHandler);
                delete letterOverlay._letterCloseHandler;
            }
        }, 1200);
    }
}

// Add keyboard shortcuts for fun
document.addEventListener('keydown', function(e) {
    if (e.key === 'y' || e.key === 'Y') {
        yesBtn.click();
    }
});
