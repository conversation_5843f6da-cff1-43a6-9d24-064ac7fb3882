<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💖 A Special Question For แหนม 💖</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Sriracha&family=Dancing+Script:wght@400;600;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Background hearts -->
        <div class="hearts-background">
            <div class="heart heart1">💖</div>
            <div class="heart heart2">💕</div>
            <div class="heart heart3">💗</div>
            <div class="heart heart4">💖</div>
            <div class="heart heart5">💕</div>
            <div class="heart heart6">💗</div>
        </div>

        <!-- Main content -->
        <div class="main-content">
            <div class="question-card">
                <div class="cute-emoji">🌸✨💖✨🌸</div>
                <h1 class="main-question">แหนมจะเป็นแฟนกับเค้ามั้ย?</h1>
                <div class="cute-message">
                    <p>ทุกครั้งที่ได้คุยกับใส้กรอก หัวใจฉันเต้นแรงมาก... 💕</p>
                    <p>เลยอยากจะถามแหนมว่า...</p>
                </div>

                <div class="buttons-container">
                    <button id="yesBtn" class="btn yes-btn">ตกลง 💖</button>
                    <button id="noBtn" class="btn no-btn">ไม่เอา 😢</button>
                </div>

                <div id="hoverMessage" class="hover-message">
                    ไม่มีทางเลือก "ไม่เอา" นะแหนม 😉
                </div>
            </div>
        </div>

        <!-- Enhanced Celebration overlay -->
        <div id="celebrationOverlay" class="celebration-overlay">
            <div class="celebration-content">
                <div class="celebration-emoji">🎉💖🎊</div>
                <h2 class="celebration-title">ใช่! 🥳</h2>

                <!-- Multiple celebration messages that rotate -->
                <div class="celebration-messages">
                    <p class="celebration-message active">
                        แหนมทำให้เค้าเป็นคนที่มีความสุขที่สุดในโลก! 💕<br>
                        รอไม่ไหวแล้วที่จะสร้างความทรงจำดีๆ ร่วมกับใส้กรอกน้าาااา! ✨
                    </p>
                    <p class="celebration-message">
                        เย่! แหนมตอบตกลง! 🎊<br>
                        เค้าจะดูแลใส้กรอกดีๆ นะ สัญญา! 💍
                    </p>
                    <p class="celebration-message">
                        ขอบคุณที่ให้โอกาสเค้านะแหนม 🥺💕<br>
                        จะทำให้เธอมีความสุขทุกวันเลย! 🌟
                    </p>
                    <p class="celebration-message">
                        ตอนนี้เราเป็นแฟนกันแล้ว! 👫💖<br>
                        อยากจะบอกทั้งโลกว่าใส้กรอกเป็นแฟนเค้า! 📢✨
                    </p>
                </div>

                <!-- Enhanced hearts with different animations -->
                <div class="celebration-hearts">
                    <span class="heart-bounce">💖</span>
                    <span class="heart-pulse">💕</span>
                    <span class="heart-spin">💗</span>
                    <span class="heart-bounce">💖</span>
                    <span class="heart-pulse">💕</span>
                    <span class="heart-spin">💗</span>
                </div>

                <!-- New celebration buttons -->
                <div class="celebration-actions">
                    <button id="shareBtn" class="celebration-btn share-btn">
                        📱 แชร์ความสุข
                    </button>
                    <button id="photoBtn" class="celebration-btn photo-btn">
                        📸 ถ่ายรูปคู่
                    </button>
                    <button id="surpriseBtn" class="celebration-btn surprise-btn">
                        🎁 เซอร์ไพรส์พิเศษ
                    </button>
                </div>

                <!-- Photo booth overlay (hidden initially) -->
                <div id="photoBooth" class="photo-booth hidden">
                    <div class="photo-frame">
                        <div class="photo-content">
                            <div class="couple-emoji">�</div>
                            <div class="photo-text">
                                <h3>เค้า ❤️ แหนม</h3>
                                <p>Official Couple Photo �💕</p>
                                <p class="date" id="coupleDate"></p>
                            </div>
                        </div>
                        <button id="closePhoto" class="close-photo">❌</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Confetti canvas -->
        <canvas id="confettiCanvas"></canvas>
    </div>

    <script src="../js/script.js"></script>
</body>
</html>