/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #ffeef0 0%, #fff5f7 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
}

/* Background hearts animation */
.hearts-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    opacity: 0.1; /* Much more subtle */
}

.heart {
    position: absolute;
    font-size: 1.5rem; /* Smaller */
    animation: float 8s ease-in-out infinite; /* Slower */
    opacity: 0.3;
}

.heart1 { top: 10%; left: 10%; animation-delay: 0s; }
.heart2 { top: 20%; right: 15%; animation-delay: 1s; }
.heart3 { top: 60%; left: 5%; animation-delay: 2s; }
.heart4 { bottom: 20%; right: 10%; animation-delay: 3s; }
.heart5 { top: 70%; right: 20%; animation-delay: 4s; }
.heart6 { bottom: 10%; left: 20%; animation-delay: 5s; }

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

/* Floating images styles */
.floating-image {
    border-radius: 50%;
    box-shadow: 0 4px 15px rgba(255, 182, 193, 0.3);
    transition: opacity 0.3s ease;
}

/* Main content */
.main-content {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 500px;
}

.question-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 30px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(255, 105, 180, 0.3);
    border: 3px solid #ffb6c1;
    backdrop-filter: blur(10px);
    animation: cardPulse 2s ease-in-out infinite;
}

@keyframes cardPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

.cute-emoji {
    font-size: 2.5rem;
    margin-bottom: 20px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.main-question {
    font-family: 'Sriracha', cursive;
    font-size: 3.2rem;
    font-weight: 400;
    color: #d63384;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(214, 51, 132, 0.2);
}

.cute-message {
    margin-bottom: 30px;
    color: #8b5a83;
    font-size: 1.1rem;
    line-height: 1.6;
}

.cute-message p {
    margin-bottom: 10px;
}

/* Buttons */
.buttons-container {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.btn {
    padding: 15px 30px;
    font-size: 1.3rem;
    font-weight: 400;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Sriracha', sans-serif;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.yes-btn {
    background: linear-gradient(45deg, #ff6b9d, #ff8fab);
    color: white;
    border: 3px solid #ff4081;
}

.yes-btn:hover {
    background: linear-gradient(45deg, #ff4081, #ff6b9d);
    transform: translateY(-3px);
    box-shadow: 0 12px 25px rgba(255, 64, 129, 0.4);
}

.no-btn {
    background: linear-gradient(45deg, #ffc1cc, #ffb3ba);
    color: #8b5a83;
    border: 3px solid #ffb3ba;
    transition: all 0.3s ease;
}

.no-btn:hover {
    animation: runAway 0.5s ease-in-out;
}

@keyframes runAway {
    0% { transform: translateX(0); }
    25% { transform: translateX(100px); }
    50% { transform: translateX(-100px); }
    75% { transform: translateX(50px); }
    100% { transform: translateX(-50px); }
}

/* Hover message */
.hover-message {
    opacity: 0;
    color: #d63384;
    font-weight: 500;
    font-size: 1rem;
    transition: opacity 0.3s ease;
    margin-top: 10px;
}

.hover-message.show {
    opacity: 1;
    animation: wiggle 0.5s ease-in-out;
}

@keyframes wiggle {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(5deg); }
    75% { transform: rotate(-5deg); }
}

/* Celebration overlay */
.celebration-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 182, 193, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.5s ease;
}

.celebration-overlay.show {
    opacity: 1;
    visibility: visible;
}

.celebration-content {
    text-align: center;
    color: #d63384;
    animation: celebrationBounce 1s ease-out;
}

@keyframes celebrationBounce {
    0% { transform: scale(0.3) rotate(-10deg); opacity: 0; }
    50% { transform: scale(1.1) rotate(5deg); }
    100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

.celebration-emoji {
    font-size: 4rem;
    margin-bottom: 20px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.celebration-title {
    font-family: 'Sriracha', cursive;
    font-size: 4.5rem;
    font-weight: 400;
    margin-bottom: 20px;
    text-shadow: 3px 3px 6px rgba(214, 51, 132, 0.3);
}

.celebration-message {
    font-size: 1.3rem;
    line-height: 1.6;
    margin-bottom: 30px;
    max-width: 600px;
}

.celebration-hearts {
    font-size: 2rem;
}

.celebration-hearts span {
    display: inline-block;
    animation: heartBeat 1.5s ease-in-out infinite;
    margin: 0 10px;
}

.celebration-hearts span:nth-child(1) { animation-delay: 0s; }
.celebration-hearts span:nth-child(2) { animation-delay: 0.2s; }
.celebration-hearts span:nth-child(3) { animation-delay: 0.4s; }
.celebration-hearts span:nth-child(4) { animation-delay: 0.6s; }
.celebration-hearts span:nth-child(5) { animation-delay: 0.8s; }

@keyframes heartBeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.3); }
}

/* Confetti canvas */
#confettiCanvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 999;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .question-card {
        padding: 30px 20px;
        margin: 10px;
    }

    .main-question {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    .cute-message {
        font-size: 1.1rem;
        line-height: 1.5;
    }

    .buttons-container {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 220px;
        margin: 5px 0;
        font-size: 1.2rem;
    }

    .celebration-title {
        font-size: 3.5rem;
    }

    .celebration-message {
        font-size: 1.2rem;
        padding: 0 20px;
        line-height: 1.6;
    }
}

@media (max-width: 480px) {
    .main-question {
        font-size: 2rem;
        line-height: 1.3;
    }

    .cute-emoji {
        font-size: 2rem;
    }

    .cute-message {
        font-size: 1rem;
    }

    .btn {
        width: 180px;
        font-size: 1.1rem;
    }

    .celebration-emoji {
        font-size: 3rem;
    }

    .celebration-title {
        font-size: 2.8rem;
    }

    .celebration-message {
        font-size: 1rem;
    }
}
