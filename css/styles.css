/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #ffeef0 0%, #fff5f7 100%);
    min-height: 100vh;
    overflow-x: hidden;
    /* Better mobile scrolling */
    -webkit-overflow-scrolling: touch;
    /* Prevent zoom on double tap */
    touch-action: manipulation;
}

/* Apply Sriracha font to all Thai text elements */
.main-question,
.celebration-title-minimal,
.celebration-subtitle,
.slide-caption,
.celebration-message-simple,
.celebration-message-simple p,
.hover-message,
.cute-message,
.cute-message p,
.yes-btn,
.no-btn,
.btn-text,
.slide-counter,
.photo-text h3,
.photo-text p {
    font-family: 'Sriracha', cursive !important;
}

/* Additional rule to catch any Thai text that might be missed */
/* This targets elements containing Thai Unicode characters (U+0E00-U+0E7F) */
*:lang(th) {
    font-family: 'Srirac<PERSON>', cursive !important;
}

.container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
}

/* Background hearts animation */
.hearts-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    opacity: 0.1; /* Much more subtle */
}

.heart {
    position: absolute;
    font-size: 1.5rem; /* Smaller */
    animation: float 8s ease-in-out infinite; /* Slower */
    opacity: 0.3;
}

.heart1 { top: 10%; left: 10%; animation-delay: 0s; }
.heart2 { top: 20%; right: 15%; animation-delay: 1s; }
.heart3 { top: 60%; left: 5%; animation-delay: 2s; }
.heart4 { bottom: 20%; right: 10%; animation-delay: 3s; }
.heart5 { top: 70%; right: 20%; animation-delay: 4s; }
.heart6 { bottom: 10%; left: 20%; animation-delay: 5s; }

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

/* Floating images styles */
.floating-image {
    border-radius: 50%;
    box-shadow: 0 4px 15px rgba(255, 182, 193, 0.3);
    transition: opacity 0.3s ease;
}

/* Main content */
.main-content {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 500px;
    opacity: 0;
    transform: translateY(30px);
    animation: slideIn 1s ease-out 0.5s forwards;
}

.question-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 30px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(255, 105, 180, 0.3);
    border: 3px solid #ffb6c1;
    backdrop-filter: blur(10px);
    animation: cardPulse 2s ease-in-out infinite;
}

@keyframes cardPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

.cute-emoji {
    font-size: 2.5rem;
    margin-bottom: 20px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.main-question {
    font-family: 'Sriracha', cursive !important;
    font-size: 3.2rem;
    font-weight: 400;
    color: #d63384;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(214, 51, 132, 0.2);
}

.cute-message {
    margin-bottom: 30px;
    color: #8b5a83;
    font-size: 1.1rem;
    line-height: 1.6;
}

.cute-message p {
    margin-bottom: 10px;
}

/* Buttons */
.buttons-container {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 20px;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.btn {
    padding: 15px 30px;
    font-size: 1.3rem;
    font-weight: 400;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Sriracha', cursive !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    /* Better touch interaction */
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
    /* Minimum touch target size */
    min-height: 44px;
    min-width: 44px;
}

.yes-btn {
    background: linear-gradient(45deg, #ff6b9d, #ff8fab);
    color: white;
    border: 3px solid #ff4081;
}

.yes-btn:hover {
    background: linear-gradient(45deg, #ff4081, #ff6b9d);
    transform: translateY(-3px);
    box-shadow: 0 12px 25px rgba(255, 64, 129, 0.4);
}

.no-btn {
    background: linear-gradient(45deg, #ffc1cc, #ffb3ba);
    color: #8b5a83;
    border: 3px solid #ffb3ba;
    transition: all 0.3s ease;
}

.no-btn:hover {
    animation: runAway 0.5s ease-in-out;
}

@keyframes runAway {
    0% { transform: translateX(0); }
    25% { transform: translateX(100px); }
    50% { transform: translateX(-100px); }
    75% { transform: translateX(50px); }
    100% { transform: translateX(-50px); }
}

/* Hover message */
.hover-message {
    opacity: 0;
    color: #d63384;
    font-weight: 500;
    font-size: 1rem;
    transition: opacity 0.3s ease;
    margin-top: 10px;
}

.hover-message.show {
    opacity: 1;
    animation: wiggle 0.5s ease-in-out;
}

@keyframes wiggle {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(5deg); }
    75% { transform: rotate(-5deg); }
}

/* Enhanced Celebration overlay with photos */
.celebration-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 240, 245, 0.98), rgba(255, 245, 250, 0.98));
    backdrop-filter: blur(20px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.8s ease;
    overflow: hidden;
}

/* Floating photo circles in background */
.celebration-photos {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.photo-circle {
    position: absolute;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    opacity: 0.3;
    animation: photoFloat 8s ease-in-out infinite;
    box-shadow: 0 10px 30px rgba(255, 105, 180, 0.2);
    border: 3px solid rgba(255, 255, 255, 0.5);
}

.photo-circle img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Fix orientations for floating background photos - no rotation */
.photo-circle img[src*="HEIF Image 3.png"] {
    transform: rotate(0deg);
}

.photo-circle img[src*="HEIF Image 5.png"] {
    transform: rotate(0deg);
}

.photo-circle img[src*="510434506_1256081902761270_5304385400308496768_n.png"] {
    transform: rotate(0deg);
}

.photo-1 { top: 10%; left: 5%; animation-delay: 0s; }
.photo-2 { top: 15%; right: 8%; animation-delay: 1s; }
.photo-3 { top: 60%; left: 3%; animation-delay: 2s; }
.photo-4 { bottom: 20%; right: 5%; animation-delay: 3s; }
.photo-5 { top: 70%; right: 15%; animation-delay: 4s; }
.photo-6 { bottom: 15%; left: 10%; animation-delay: 5s; }

.celebration-overlay.show {
    opacity: 1;
    visibility: visible;
}

.celebration-content {
    text-align: center;
    color: #d63384;
    animation: celebrationBounce 1s ease-out;
    position: relative;
    z-index: 2;
    max-width: 600px;
    padding: 20px;
}

/* Minimalistic celebration header */
.celebration-header {
    margin-bottom: 30px;
}

.success-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2.5rem;
    color: white;
    font-weight: bold;
    animation: successPulse 2s ease-in-out infinite;
    box-shadow: 0 10px 30px rgba(76, 175, 80, 0.3);
}

.celebration-title-minimal {
    font-family: 'Sriracha', cursive !important;
    font-size: 2.5rem;
    font-weight: 400;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(214, 51, 132, 0.2);
    color: #d63384;
}

.celebration-subtitle {
    font-size: 1.5rem;
    color: #ff69b4;
    font-weight: 500;
    margin-bottom: 30px;
}

/* Photo slideshow styles */
.memory-slideshow {
    margin-bottom: 30px;
    position: relative;
}

.slideshow-container {
    position: relative;
    width: 100%;
    max-width: 400px;
    height: 300px;
    margin: 0 auto;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

/* Navigation arrows */
.slide-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 1.2rem;
    color: #d63384;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.slide-arrow:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.slide-arrow-left {
    left: 15px;
}

.slide-arrow-right {
    right: 15px;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.8s ease-in-out;
}

.slide.active {
    opacity: 1;
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Easy rotation classes - change the numbers to fix แหนม's face position */

/* HEIF Image 3 - No rotation, keep original orientation */
.slide img[src*="HEIF Image 3.png"] {
    transform: rotate(0deg); /* Original orientation */
}

/* HEIF Image 5 - No rotation, keep original orientation */
.slide img[src*="HEIF Image 5.png"] {
    transform: rotate(0deg); /* Original orientation */
}

/* Long filename photo - No rotation, keep original orientation */
.slide img[src*="510434506_1256081902761270_5304385400308496768_n.png"] {
    transform: rotate(0deg); /* Original orientation */
}

.slide-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 20px;
    font-size: 1.1rem;
    text-align: center;
}

.slideshow-nav {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.nav-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: rgba(214, 51, 132, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-dot.active,
.nav-dot:hover {
    background: #d63384;
    transform: scale(1.2);
}

/* Slide counter */
.slide-counter {
    text-align: center;
    margin-top: 15px;
    font-size: 0.9rem;
    color: #8b5a83;
    font-weight: 500;
}

@keyframes celebrationBounce {
    0% { transform: scale(0.3) rotate(-10deg); opacity: 0; }
    50% { transform: scale(1.1) rotate(5deg); }
    100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

.celebration-emoji {
    font-size: 4rem;
    margin-bottom: 20px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.celebration-title {
    font-family: 'Sriracha', cursive !important;
    font-size: 4.5rem;
    font-weight: 400;
    margin-bottom: 20px;
    text-shadow: 3px 3px 6px rgba(214, 51, 132, 0.3);
}

.celebration-message {
    font-size: 1.3rem;
    line-height: 1.6;
    margin-bottom: 30px;
    max-width: 600px;
}

.celebration-hearts {
    font-size: 2rem;
}

/* Enhanced celebration messages */
.celebration-messages {
    position: relative;
    height: 120px;
    margin-bottom: 30px;
}

.celebration-message {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.8s ease-in-out;
    font-size: 1.3rem;
    line-height: 1.6;
    max-width: 600px;
}

.celebration-message.active {
    opacity: 1;
    transform: translateY(0);
}

/* Enhanced hearts with different animations */
.celebration-hearts {
    font-size: 2rem;
    margin-bottom: 30px;
}

.celebration-hearts span {
    display: inline-block;
    margin: 0 10px;
}

.heart-bounce {
    animation: heartBounce 1.5s ease-in-out infinite;
}

.heart-pulse {
    animation: heartPulse 2s ease-in-out infinite;
}

.heart-spin {
    animation: heartSpin 3s linear infinite;
}

.celebration-hearts .heart-bounce:nth-child(1) { animation-delay: 0s; }
.celebration-hearts .heart-pulse:nth-child(2) { animation-delay: 0.3s; }
.celebration-hearts .heart-spin:nth-child(3) { animation-delay: 0.6s; }
.celebration-hearts .heart-bounce:nth-child(4) { animation-delay: 0.9s; }
.celebration-hearts .heart-pulse:nth-child(5) { animation-delay: 1.2s; }
.celebration-hearts .heart-spin:nth-child(6) { animation-delay: 1.5s; }

/* New heart animations */
@keyframes heartBounce {
    0%, 100% { transform: scale(1) translateY(0); }
    50% { transform: scale(1.3) translateY(-10px); }
}

@keyframes heartPulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.5); opacity: 0.7; }
}

@keyframes heartSpin {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.2); }
    100% { transform: rotate(360deg) scale(1); }
}

/* Celebration action buttons */
.celebration-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 20px;
}

.celebration-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    font-family: 'Poppins', sans-serif;
}

.share-btn {
    background: linear-gradient(45deg, #ff6b9d, #ff8fab);
    color: white;
}

.photo-btn {
    background: linear-gradient(45deg, #ffc1cc, #ffb3ba);
    color: #8b5a83;
}

.surprise-btn {
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    color: white;
}

.celebration-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Photo booth styles */
.photo-booth {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    transition: all 0.5s ease;
}

.photo-booth.hidden {
    opacity: 0;
    visibility: hidden;
}

.photo-frame {
    background: white;
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    text-align: center;
    position: relative;
    max-width: 400px;
    animation: photoAppear 0.8s ease-out;
}

.couple-emoji {
    font-size: 4rem;
    margin-bottom: 20px;
}

.photo-text h3 {
    font-size: 2rem;
    color: #d63384;
    margin-bottom: 10px;
    font-family: 'Sriracha', cursive !important;
}

.photo-text p {
    color: #8b5a83;
    margin-bottom: 5px;
}

.date {
    font-weight: 600;
    color: #ff69b4;
}

.close-photo {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.close-photo:hover {
    opacity: 1;
}

/* New animations for enhanced features */
@keyframes slideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeUp {
    0% {
        opacity: 1;
        transform: translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateY(-30px);
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes photoAppear {
    0% {
        transform: scale(0.3) rotate(-10deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.1) rotate(5deg);
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

@keyframes surpriseFloat {
    0% {
        transform: scale(0) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.5) rotate(180deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(0.5) rotate(360deg) translateY(-100px);
        opacity: 0;
    }
}

/* Simple celebration message */
.celebration-message-simple {
    margin-bottom: 30px;
    font-size: 1.2rem;
    line-height: 1.6;
    color: #8b5a83;
}

.celebration-message-simple p {
    margin-bottom: 10px;
}

/* Minimalistic action buttons */
.celebration-actions-minimal {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.minimal-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 25px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Poppins', sans-serif;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    /* Better touch interaction */
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
    min-height: 44px;
    min-width: 44px;
}

.minimal-btn.primary {
    background: linear-gradient(135deg, #ff6b9d, #ff8fab);
    color: white;
}

.minimal-btn.secondary {
    background: linear-gradient(135deg, #ffc1cc, #ffb3ba);
    color: #8b5a83;
}

.minimal-btn.letter {
    background: linear-gradient(135deg, #e91e63, #f06292);
    color: white;
}

.minimal-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.minimal-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Ensure buttons are clickable */
.celebration-actions-minimal {
    position: relative;
    z-index: 100;
}

.minimal-btn {
    position: relative;
    z-index: 101;
    pointer-events: auto;
}

.btn-icon {
    font-size: 1.2rem;
}

/* Floating hearts */
.floating-hearts {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.heart-float {
    position: absolute;
    font-size: 1.5rem;
    opacity: 0.6;
    animation: heartFloat 6s ease-in-out infinite;
}

.heart-1 { top: 20%; left: 15%; animation-delay: 0s; }
.heart-2 { top: 30%; right: 20%; animation-delay: 1.2s; }
.heart-3 { bottom: 30%; left: 20%; animation-delay: 2.4s; }
.heart-4 { bottom: 20%; right: 15%; animation-delay: 3.6s; }
.heart-5 { top: 50%; left: 10%; animation-delay: 4.8s; }

@keyframes photoFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
    50% { transform: translateY(-15px) rotate(5deg); opacity: 0.6; }
}

@keyframes heartFloat {
    0%, 100% { transform: translateY(0px) scale(1); opacity: 0.6; }
    50% { transform: translateY(-20px) scale(1.2); opacity: 0.8; }
}

@keyframes successPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes messagePopup {
    0% {
        transform: translateX(-50%) scale(0.3);
        opacity: 0;
    }
    20% {
        transform: translateX(-50%) scale(1.1);
        opacity: 1;
    }
    80% {
        transform: translateX(-50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateX(-50%) scale(0.3);
        opacity: 0;
    }
}

/* Confetti canvas */
#confettiCanvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 999;
}

/* ===== COMPREHENSIVE RESPONSIVE DESIGN ===== */

/* Large tablets and small desktops (1024px and down) */
@media (max-width: 1024px) {
    .container {
        padding: 10px;
    }

    .slideshow-container {
        max-width: 500px;
        max-height: 400px;
    }

    .slide img {
        max-height: 350px;
    }

    .celebration-photos .photo-circle {
        width: 80px;
        height: 80px;
    }

    .celebration-photos .photo-circle img {
        width: 80px;
        height: 80px;
    }

    .celebration-actions-minimal {
        flex-wrap: wrap;
        justify-content: center;
        gap: 15px;
    }
}

/* Tablets (768px and down) */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .question-card {
        margin: 15px;
        padding: 25px 20px;
        border-radius: 20px;
        max-width: 90%;
    }

    .main-question {
        font-size: 2.2rem;
        line-height: 1.3;
        margin-bottom: 15px;
    }

    .cute-message {
        font-size: 1.1rem;
        line-height: 1.5;
        margin-bottom: 25px;
    }

    .cute-message p {
        margin-bottom: 12px;
    }

    .buttons-container {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .btn {
        width: 200px;
        margin: 8px 0;
        font-size: 1.2rem;
        padding: 12px 25px;
        border-radius: 40px;
    }

    /* Celebration overlay adjustments */
    .celebration-content {
        padding: 20px;
        margin: 20px;
        max-width: 90%;
        border-radius: 20px;
    }

    .celebration-title-minimal {
        font-size: 2rem;
        margin-bottom: 8px;
    }

    .celebration-subtitle {
        font-size: 1.2rem;
        margin-bottom: 20px;
    }

    .slideshow-container {
        max-width: 400px;
        max-height: 300px;
        margin: 20px auto;
    }

    .slide img {
        max-height: 250px;
        border-radius: 15px;
    }

    .slide-caption {
        font-size: 1rem;
        padding: 8px 15px;
        border-radius: 15px;
    }

    .slide-arrow {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }

    .slideshow-nav {
        margin-top: 15px;
    }

    .nav-dot {
        width: 10px;
        height: 10px;
        margin: 0 4px;
    }

    .slide-counter {
        font-size: 0.9rem;
        margin-top: 10px;
    }

    .celebration-message-simple {
        margin: 20px 0;
    }

    .celebration-message-simple p {
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 10px;
    }

    .celebration-actions-minimal {
        flex-direction: column;
        gap: 12px;
        margin-top: 25px;
        align-items: center;
    }

    .minimal-btn {
        width: 200px;
        padding: 12px 20px;
        font-size: 1rem;
    }

    /* Background elements */
    .celebration-photos .photo-circle {
        width: 60px;
        height: 60px;
    }

    .celebration-photos .photo-circle img {
        width: 60px;
        height: 60px;
    }

    .hearts-background .heart {
        font-size: 1.5rem;
    }

    .floating-hearts .heart-float {
        font-size: 1.2rem;
    }

    .celebration-title {
        font-size: 3.5rem;
    }

    .celebration-message {
        font-size: 1.2rem;
        padding: 0 20px;
        line-height: 1.6;
    }
}

/* Mobile phones (480px and down) */
@media (max-width: 480px) {
    .container {
        padding: 10px;
    }

    .question-card {
        margin: 10px;
        padding: 20px 15px;
        border-radius: 15px;
        max-width: 95%;
    }

    .main-question {
        font-size: 1.8rem;
        line-height: 1.2;
        margin-bottom: 12px;
    }

    .cute-emoji {
        font-size: 1.8rem;
        margin-bottom: 15px;
    }

    .cute-message {
        font-size: 0.95rem;
        line-height: 1.4;
        margin-bottom: 20px;
    }

    .cute-message p {
        margin-bottom: 10px;
    }

    .btn {
        width: 180px;
        font-size: 1.1rem;
        padding: 10px 20px;
        border-radius: 35px;
    }

    .buttons-container {
        gap: 12px;
    }

    /* Celebration overlay for mobile */
    .celebration-content {
        padding: 15px;
        margin: 10px;
        max-width: 95%;
        border-radius: 15px;
    }

    .celebration-title-minimal {
        font-size: 1.6rem;
        margin-bottom: 6px;
    }

    .celebration-subtitle {
        font-size: 1rem;
        margin-bottom: 15px;
    }

    .slideshow-container {
        max-width: 320px;
        max-height: 240px;
        margin: 15px auto;
    }

    .slide img {
        max-height: 200px;
        border-radius: 12px;
    }

    .slide-caption {
        font-size: 0.9rem;
        padding: 6px 12px;
        border-radius: 12px;
    }

    .slide-arrow {
        width: 30px;
        height: 30px;
        font-size: 14px;
    }

    .nav-dot {
        width: 8px;
        height: 8px;
        margin: 0 3px;
    }

    .slide-counter {
        font-size: 0.8rem;
        margin-top: 8px;
    }

    .celebration-message-simple {
        margin: 15px 0;
    }

    .celebration-message-simple p {
        font-size: 1rem;
        line-height: 1.5;
        margin-bottom: 8px;
    }

    .celebration-actions-minimal {
        flex-direction: column;
        gap: 10px;
        margin-top: 20px;
    }

    .minimal-btn {
        width: 180px;
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    /* Background elements for mobile */
    .celebration-photos .photo-circle {
        width: 45px;
        height: 45px;
    }

    .celebration-photos .photo-circle img {
        width: 45px;
        height: 45px;
    }

    .hearts-background .heart {
        font-size: 1.2rem;
    }

    .floating-hearts .heart-float {
        font-size: 1rem;
    }

    .celebration-emoji {
        font-size: 2.5rem;
    }

    .celebration-title {
        font-size: 2.5rem;
    }

    .celebration-message {
        font-size: 0.95rem;
        padding: 0 15px;
        line-height: 1.5;
    }

    .hover-message {
        font-size: 0.9rem;
        padding: 8px 15px;
        border-radius: 15px;
    }
}

/* Extra small devices (360px and down) */
@media (max-width: 360px) {
    .question-card {
        margin: 8px;
        padding: 18px 12px;
    }

    .main-question {
        font-size: 1.6rem;
        line-height: 1.1;
    }

    .cute-emoji {
        font-size: 1.6rem;
    }

    .cute-message {
        font-size: 0.9rem;
    }

    .btn {
        width: 160px;
        font-size: 1rem;
        padding: 9px 18px;
    }

    .celebration-content {
        padding: 12px;
        margin: 8px;
    }

    .celebration-title-minimal {
        font-size: 1.4rem;
    }

    .slideshow-container {
        max-width: 280px;
        max-height: 200px;
    }

    .slide img {
        max-height: 160px;
    }

    .slide-caption {
        font-size: 0.8rem;
        padding: 5px 10px;
    }

    .minimal-btn {
        width: 160px;
        padding: 9px 12px;
        font-size: 0.85rem;
    }

    .celebration-message-simple p {
        font-size: 0.9rem;
    }
}

/* Letter Modal/Overlay Styles */
.letter-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: fadeIn 0.5s ease-in-out;
}

.letter-overlay.show {
    display: flex;
}

.letter-container {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.letter-close-btn {
    position: absolute;
    top: -20px;
    right: -20px;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10001;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.letter-close-btn:hover {
    background: white;
    transform: scale(1.1);
}

/* Envelope Styles */
.envelope-container {
    perspective: 1000px;
}

.envelope {
    position: relative;
    width: 400px;
    height: 280px;
    cursor: pointer;
    transition: transform 0.6s ease;
}

.envelope:hover {
    transform: scale(1.05);
}

.envelope-body {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 3px solid #d63384;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.envelope-flap {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(135deg, #ff6b9d, #ff8fab);
    transform-origin: top center;
    transition: transform 0.8s ease;
    z-index: 2;
    border-radius: 8px 8px 0 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.envelope-flap::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 200px solid transparent;
    border-right: 200px solid transparent;
    border-top: 100px solid #ff8fab;
}

.envelope.opened .envelope-flap {
    transform: rotateX(-180deg);
}

/* Letter Paper Styles */
.letter-paper {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    background: #fff;
    border-radius: 5px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(100%);
    transition: transform 0.8s ease 0.3s;
    overflow-y: auto;
    z-index: 1;
}

.envelope.opened .letter-paper {
    transform: translateY(0);
}

.letter-content {
    font-family: 'Sriracha', cursive !important;
    color: #333;
    line-height: 1.8;
}

.letter-header {
    text-align: center;
    margin-bottom: 30px;
    border-bottom: 2px solid #ff6b9d;
    padding-bottom: 20px;
}

.letter-date {
    font-size: 1rem;
    color: #8b5a83;
    margin-bottom: 10px;
}

.letter-to {
    font-size: 1.4rem;
    color: #d63384;
    font-weight: 600;
}

.letter-body {
    margin-bottom: 30px;
}

.letter-body p {
    margin-bottom: 20px;
    font-size: 1.1rem;
    text-align: left;
}

.letter-footer {
    text-align: right;
    border-top: 1px solid #ffc1cc;
    padding-top: 20px;
}

.letter-signature p {
    margin-bottom: 10px;
    font-size: 1rem;
    color: #8b5a83;
}

.signature-name {
    font-size: 1.3rem !important;
    color: #d63384 !important;
    font-weight: 600;
}

/* Animation for envelope opening */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes letterSlideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* ===== LETTER MODAL RESPONSIVE DESIGN ===== */

/* Tablets (768px and down) */
@media (max-width: 768px) {
    .letter-container {
        max-width: 95%;
        max-height: 95%;
        padding: 10px;
    }

    .letter-close-btn {
        top: -15px;
        right: -15px;
        width: 35px;
        height: 35px;
        font-size: 20px;
    }

    .envelope {
        width: 350px;
        height: 250px;
    }

    .letter-paper {
        padding: 18px;
        top: 15px;
        left: 15px;
        right: 15px;
        bottom: 15px;
    }

    .letter-content {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .letter-header {
        margin-bottom: 20px;
        padding-bottom: 15px;
    }

    .letter-date {
        font-size: 0.9rem;
    }

    .letter-to {
        font-size: 1.2rem;
    }

    .letter-body p {
        font-size: 1rem;
        margin-bottom: 15px;
    }

    .letter-footer {
        padding-top: 15px;
    }

    .letter-signature p {
        font-size: 0.9rem;
    }

    .signature-name {
        font-size: 1.1rem !important;
    }
}

/* Mobile phones (480px and down) */
@media (max-width: 480px) {
    .letter-container {
        max-width: 98%;
        max-height: 98%;
        padding: 5px;
    }

    .letter-close-btn {
        top: -12px;
        right: -12px;
        width: 30px;
        height: 30px;
        font-size: 18px;
    }

    .envelope {
        width: 300px;
        height: 220px;
    }

    .letter-paper {
        padding: 15px;
        top: 12px;
        left: 12px;
        right: 12px;
        bottom: 12px;
    }

    .letter-content {
        font-size: 0.85rem;
        line-height: 1.5;
    }

    .letter-header {
        margin-bottom: 15px;
        padding-bottom: 12px;
    }

    .letter-date {
        font-size: 0.8rem;
        margin-bottom: 8px;
    }

    .letter-to {
        font-size: 1.1rem;
    }

    .letter-body {
        margin-bottom: 20px;
    }

    .letter-body p {
        font-size: 0.9rem;
        margin-bottom: 12px;
        line-height: 1.4;
    }

    .letter-footer {
        padding-top: 12px;
    }

    .letter-signature p {
        font-size: 0.8rem;
        margin-bottom: 8px;
    }

    .signature-name {
        font-size: 1rem !important;
    }
}

/* Extra small devices (360px and down) */
@media (max-width: 360px) {
    .letter-container {
        padding: 2px;
    }

    .letter-close-btn {
        top: -10px;
        right: -10px;
        width: 28px;
        height: 28px;
        font-size: 16px;
    }

    .envelope {
        width: 260px;
        height: 190px;
    }

    .letter-paper {
        padding: 12px;
        top: 10px;
        left: 10px;
        right: 10px;
        bottom: 10px;
    }

    .letter-content {
        font-size: 0.8rem;
        line-height: 1.4;
    }

    .letter-header {
        margin-bottom: 12px;
        padding-bottom: 10px;
    }

    .letter-date {
        font-size: 0.75rem;
    }

    .letter-to {
        font-size: 1rem;
    }

    .letter-body p {
        font-size: 0.8rem;
        margin-bottom: 10px;
    }

    .letter-signature p {
        font-size: 0.75rem;
    }

    .signature-name {
        font-size: 0.9rem !important;
    }
}

/* ===== ADDITIONAL RESPONSIVE UTILITIES ===== */

/* Ensure images are responsive */
img {
    max-width: 100%;
    height: auto;
}

/* Better focus states for accessibility */
button:focus,
.btn:focus,
.minimal-btn:focus {
    outline: 2px solid #ff6b9d;
    outline-offset: 2px;
}

/* Smooth scrolling for better UX */
html {
    scroll-behavior: smooth;
}

/* Prevent horizontal scroll on small screens */
.container {
    max-width: 100vw;
    box-sizing: border-box;
}

/* Better text selection */
::selection {
    background: rgba(255, 107, 157, 0.3);
    color: #333;
}

::-moz-selection {
    background: rgba(255, 107, 157, 0.3);
    color: #333;
}

/* Landscape orientation adjustments for mobile */
@media (max-height: 500px) and (orientation: landscape) {
    .question-card {
        padding: 15px;
        margin: 10px;
    }

    .main-question {
        font-size: 1.5rem;
        margin-bottom: 10px;
    }

    .cute-message {
        font-size: 0.9rem;
        margin-bottom: 15px;
    }

    .btn {
        padding: 8px 20px;
        font-size: 1rem;
    }

    .celebration-content {
        padding: 15px;
        margin: 10px;
    }

    .slideshow-container {
        max-height: 200px;
    }

    .slide img {
        max-height: 150px;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .slide img,
    .photo-circle img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .envelope {
        transition: none !important;
    }

    .letter-paper {
        transition: none !important;
    }
}

/* Dark mode support (if user prefers) */
@media (prefers-color-scheme: dark) {
    /* Keep the romantic pink theme even in dark mode */
    body {
        background: linear-gradient(135deg, #2a1a1d 0%, #3d2a2f 100%);
    }

    .question-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
    }

    .celebration-content {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
    }
}
