/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #ffeef0 0%, #fff5f7 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
}

/* Background hearts animation */
.hearts-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    opacity: 0.1; /* Much more subtle */
}

.heart {
    position: absolute;
    font-size: 1.5rem; /* Smaller */
    animation: float 8s ease-in-out infinite; /* Slower */
    opacity: 0.3;
}

.heart1 { top: 10%; left: 10%; animation-delay: 0s; }
.heart2 { top: 20%; right: 15%; animation-delay: 1s; }
.heart3 { top: 60%; left: 5%; animation-delay: 2s; }
.heart4 { bottom: 20%; right: 10%; animation-delay: 3s; }
.heart5 { top: 70%; right: 20%; animation-delay: 4s; }
.heart6 { bottom: 10%; left: 20%; animation-delay: 5s; }

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

/* Floating images styles */
.floating-image {
    border-radius: 50%;
    box-shadow: 0 4px 15px rgba(255, 182, 193, 0.3);
    transition: opacity 0.3s ease;
}

/* Main content */
.main-content {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 500px;
    opacity: 0;
    transform: translateY(30px);
    animation: slideIn 1s ease-out 0.5s forwards;
}

.question-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 30px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(255, 105, 180, 0.3);
    border: 3px solid #ffb6c1;
    backdrop-filter: blur(10px);
    animation: cardPulse 2s ease-in-out infinite;
}

@keyframes cardPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

.cute-emoji {
    font-size: 2.5rem;
    margin-bottom: 20px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.main-question {
    font-family: 'Sriracha', cursive;
    font-size: 3.2rem;
    font-weight: 400;
    color: #d63384;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(214, 51, 132, 0.2);
}

.cute-message {
    margin-bottom: 30px;
    color: #8b5a83;
    font-size: 1.1rem;
    line-height: 1.6;
}

.cute-message p {
    margin-bottom: 10px;
}

/* Buttons */
.buttons-container {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 20px;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.btn {
    padding: 15px 30px;
    font-size: 1.3rem;
    font-weight: 400;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Sriracha', sans-serif;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.yes-btn {
    background: linear-gradient(45deg, #ff6b9d, #ff8fab);
    color: white;
    border: 3px solid #ff4081;
}

.yes-btn:hover {
    background: linear-gradient(45deg, #ff4081, #ff6b9d);
    transform: translateY(-3px);
    box-shadow: 0 12px 25px rgba(255, 64, 129, 0.4);
}

.no-btn {
    background: linear-gradient(45deg, #ffc1cc, #ffb3ba);
    color: #8b5a83;
    border: 3px solid #ffb3ba;
    transition: all 0.3s ease;
}

.no-btn:hover {
    animation: runAway 0.5s ease-in-out;
}

@keyframes runAway {
    0% { transform: translateX(0); }
    25% { transform: translateX(100px); }
    50% { transform: translateX(-100px); }
    75% { transform: translateX(50px); }
    100% { transform: translateX(-50px); }
}

/* Hover message */
.hover-message {
    opacity: 0;
    color: #d63384;
    font-weight: 500;
    font-size: 1rem;
    transition: opacity 0.3s ease;
    margin-top: 10px;
}

.hover-message.show {
    opacity: 1;
    animation: wiggle 0.5s ease-in-out;
}

@keyframes wiggle {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(5deg); }
    75% { transform: rotate(-5deg); }
}

/* Enhanced Celebration overlay with photos */
.celebration-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 240, 245, 0.98), rgba(255, 245, 250, 0.98));
    backdrop-filter: blur(20px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.8s ease;
    overflow: hidden;
}

/* Floating photo circles in background */
.celebration-photos {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.photo-circle {
    position: absolute;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    opacity: 0.3;
    animation: photoFloat 8s ease-in-out infinite;
    box-shadow: 0 10px 30px rgba(255, 105, 180, 0.2);
    border: 3px solid rgba(255, 255, 255, 0.5);
}

.photo-circle img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-1 { top: 10%; left: 5%; animation-delay: 0s; }
.photo-2 { top: 15%; right: 8%; animation-delay: 1s; }
.photo-3 { top: 60%; left: 3%; animation-delay: 2s; }
.photo-4 { bottom: 20%; right: 5%; animation-delay: 3s; }
.photo-5 { top: 70%; right: 15%; animation-delay: 4s; }
.photo-6 { bottom: 15%; left: 10%; animation-delay: 5s; }

.celebration-overlay.show {
    opacity: 1;
    visibility: visible;
}

.celebration-content {
    text-align: center;
    color: #d63384;
    animation: celebrationBounce 1s ease-out;
    position: relative;
    z-index: 2;
    max-width: 600px;
    padding: 20px;
}

/* Minimalistic celebration header */
.celebration-header {
    margin-bottom: 30px;
}

.success-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2.5rem;
    color: white;
    font-weight: bold;
    animation: successPulse 2s ease-in-out infinite;
    box-shadow: 0 10px 30px rgba(76, 175, 80, 0.3);
}

.celebration-title-minimal {
    font-family: 'Sriracha', cursive;
    font-size: 2.5rem;
    font-weight: 400;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(214, 51, 132, 0.2);
    color: #d63384;
}

.celebration-subtitle {
    font-size: 1.5rem;
    color: #ff69b4;
    font-weight: 500;
    margin-bottom: 30px;
}

/* Photo slideshow styles */
.memory-slideshow {
    margin-bottom: 30px;
    position: relative;
}

.slideshow-container {
    position: relative;
    width: 100%;
    max-width: 400px;
    height: 300px;
    margin: 0 auto;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

/* Navigation arrows */
.slide-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 1.2rem;
    color: #d63384;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.slide-arrow:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.slide-arrow-left {
    left: 15px;
}

.slide-arrow-right {
    right: 15px;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.8s ease-in-out;
}

.slide.active {
    opacity: 1;
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slide-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 20px;
    font-size: 1.1rem;
    text-align: center;
}

.slideshow-nav {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.nav-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: rgba(214, 51, 132, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-dot.active,
.nav-dot:hover {
    background: #d63384;
    transform: scale(1.2);
}

/* Slide counter */
.slide-counter {
    text-align: center;
    margin-top: 15px;
    font-size: 0.9rem;
    color: #8b5a83;
    font-weight: 500;
}

@keyframes celebrationBounce {
    0% { transform: scale(0.3) rotate(-10deg); opacity: 0; }
    50% { transform: scale(1.1) rotate(5deg); }
    100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

.celebration-emoji {
    font-size: 4rem;
    margin-bottom: 20px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.celebration-title {
    font-family: 'Sriracha', cursive;
    font-size: 4.5rem;
    font-weight: 400;
    margin-bottom: 20px;
    text-shadow: 3px 3px 6px rgba(214, 51, 132, 0.3);
}

.celebration-message {
    font-size: 1.3rem;
    line-height: 1.6;
    margin-bottom: 30px;
    max-width: 600px;
}

.celebration-hearts {
    font-size: 2rem;
}

/* Enhanced celebration messages */
.celebration-messages {
    position: relative;
    height: 120px;
    margin-bottom: 30px;
}

.celebration-message {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.8s ease-in-out;
    font-size: 1.3rem;
    line-height: 1.6;
    max-width: 600px;
}

.celebration-message.active {
    opacity: 1;
    transform: translateY(0);
}

/* Enhanced hearts with different animations */
.celebration-hearts {
    font-size: 2rem;
    margin-bottom: 30px;
}

.celebration-hearts span {
    display: inline-block;
    margin: 0 10px;
}

.heart-bounce {
    animation: heartBounce 1.5s ease-in-out infinite;
}

.heart-pulse {
    animation: heartPulse 2s ease-in-out infinite;
}

.heart-spin {
    animation: heartSpin 3s linear infinite;
}

.celebration-hearts .heart-bounce:nth-child(1) { animation-delay: 0s; }
.celebration-hearts .heart-pulse:nth-child(2) { animation-delay: 0.3s; }
.celebration-hearts .heart-spin:nth-child(3) { animation-delay: 0.6s; }
.celebration-hearts .heart-bounce:nth-child(4) { animation-delay: 0.9s; }
.celebration-hearts .heart-pulse:nth-child(5) { animation-delay: 1.2s; }
.celebration-hearts .heart-spin:nth-child(6) { animation-delay: 1.5s; }

/* New heart animations */
@keyframes heartBounce {
    0%, 100% { transform: scale(1) translateY(0); }
    50% { transform: scale(1.3) translateY(-10px); }
}

@keyframes heartPulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.5); opacity: 0.7; }
}

@keyframes heartSpin {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.2); }
    100% { transform: rotate(360deg) scale(1); }
}

/* Celebration action buttons */
.celebration-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 20px;
}

.celebration-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    font-family: 'Poppins', sans-serif;
}

.share-btn {
    background: linear-gradient(45deg, #ff6b9d, #ff8fab);
    color: white;
}

.photo-btn {
    background: linear-gradient(45deg, #ffc1cc, #ffb3ba);
    color: #8b5a83;
}

.surprise-btn {
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    color: white;
}

.celebration-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Photo booth styles */
.photo-booth {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    transition: all 0.5s ease;
}

.photo-booth.hidden {
    opacity: 0;
    visibility: hidden;
}

.photo-frame {
    background: white;
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    text-align: center;
    position: relative;
    max-width: 400px;
    animation: photoAppear 0.8s ease-out;
}

.couple-emoji {
    font-size: 4rem;
    margin-bottom: 20px;
}

.photo-text h3 {
    font-size: 2rem;
    color: #d63384;
    margin-bottom: 10px;
    font-family: 'Sriracha', cursive;
}

.photo-text p {
    color: #8b5a83;
    margin-bottom: 5px;
}

.date {
    font-weight: 600;
    color: #ff69b4;
}

.close-photo {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.close-photo:hover {
    opacity: 1;
}

/* New animations for enhanced features */
@keyframes slideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeUp {
    0% {
        opacity: 1;
        transform: translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateY(-30px);
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes photoAppear {
    0% {
        transform: scale(0.3) rotate(-10deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.1) rotate(5deg);
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

@keyframes surpriseFloat {
    0% {
        transform: scale(0) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.5) rotate(180deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(0.5) rotate(360deg) translateY(-100px);
        opacity: 0;
    }
}

/* Simple celebration message */
.celebration-message-simple {
    margin-bottom: 30px;
    font-size: 1.2rem;
    line-height: 1.6;
    color: #8b5a83;
}

.celebration-message-simple p {
    margin-bottom: 10px;
}

/* Minimalistic action buttons */
.celebration-actions-minimal {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.minimal-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 25px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Poppins', sans-serif;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.minimal-btn.primary {
    background: linear-gradient(135deg, #ff6b9d, #ff8fab);
    color: white;
}

.minimal-btn.secondary {
    background: linear-gradient(135deg, #ffc1cc, #ffb3ba);
    color: #8b5a83;
}

.minimal-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.minimal-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Ensure buttons are clickable */
.celebration-actions-minimal {
    position: relative;
    z-index: 100;
}

.minimal-btn {
    position: relative;
    z-index: 101;
    pointer-events: auto;
}

.btn-icon {
    font-size: 1.2rem;
}

/* Floating hearts */
.floating-hearts {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.heart-float {
    position: absolute;
    font-size: 1.5rem;
    opacity: 0.6;
    animation: heartFloat 6s ease-in-out infinite;
}

.heart-1 { top: 20%; left: 15%; animation-delay: 0s; }
.heart-2 { top: 30%; right: 20%; animation-delay: 1.2s; }
.heart-3 { bottom: 30%; left: 20%; animation-delay: 2.4s; }
.heart-4 { bottom: 20%; right: 15%; animation-delay: 3.6s; }
.heart-5 { top: 50%; left: 10%; animation-delay: 4.8s; }

@keyframes photoFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
    50% { transform: translateY(-15px) rotate(5deg); opacity: 0.6; }
}

@keyframes heartFloat {
    0%, 100% { transform: translateY(0px) scale(1); opacity: 0.6; }
    50% { transform: translateY(-20px) scale(1.2); opacity: 0.8; }
}

@keyframes successPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes messagePopup {
    0% {
        transform: translateX(-50%) scale(0.3);
        opacity: 0;
    }
    20% {
        transform: translateX(-50%) scale(1.1);
        opacity: 1;
    }
    80% {
        transform: translateX(-50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateX(-50%) scale(0.3);
        opacity: 0;
    }
}

/* Confetti canvas */
#confettiCanvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 999;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .question-card {
        margin: 20px;
        padding: 30px 25px;
    }

    .main-question {
        font-size: 2.2rem;
        line-height: 1.3;
    }

    .cute-message {
        font-size: 1.1rem;
        line-height: 1.5;
    }

    .buttons-container {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 200px;
        margin: 8px 0;
        font-size: 1.2rem;
    }

    .celebration-title {
        font-size: 3.5rem;
    }

    .celebration-message {
        font-size: 1.2rem;
        padding: 0 20px;
        line-height: 1.6;
    }
}

@media (max-width: 480px) {
    .main-question {
        font-size: 2rem;
        line-height: 1.3;
    }

    .cute-emoji {
        font-size: 2rem;
    }

    .cute-message {
        font-size: 1rem;
    }

    .btn {
        width: 180px;
        font-size: 1.1rem;
    }

    .celebration-emoji {
        font-size: 3rem;
    }

    .celebration-title {
        font-size: 2.8rem;
    }

    .celebration-message {
        font-size: 1rem;
    }
}
